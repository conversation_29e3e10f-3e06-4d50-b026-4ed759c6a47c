import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, FlatList, Alert, ActivityIndicator, ScrollView } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useNavigation } from '@react-navigation/native';
import { useAuth } from '../../hooks/useAuth';
import { useFarm } from '../../hooks/useFarm';
import { StackNavigationProp } from '@react-navigation/stack';
import { MainStackParamList } from '../../navigation/types';
import { SafeAreaView } from 'react-native-safe-area-context';
import { StatusBar } from 'expo-status-bar';

type EquipmentDiagnosticsScreenNavigationProp = StackNavigationProp<MainStackParamList, 'EquipmentDiagnostics'>;

type Equipment = {
  id: string;
  name: string;
  type: string;
  model: string;
  status: 'online' | 'offline' | 'warning' | 'error';
  lastConnected?: string;
  bluetoothId?: string;
};

type DiagnosticResult = {
  id: string;
  name: string;
  status: 'normal' | 'warning' | 'error';
  value: string;
  unit?: string;
  description?: string;
};

const EquipmentDiagnosticsScreen = () => {
  const navigation = useNavigation<EquipmentDiagnosticsScreenNavigationProp>();
  const { user } = useAuth();
  const { currentFarm } = useFarm();
  const [loading, setLoading] = useState(true);
  const [scanning, setScanning] = useState(false);
  const [bluetoothEnabled, setBluetoothEnabled] = useState<boolean | null>(null);
  const [equipment, setEquipment] = useState<Equipment[]>([
    { 
      id: '1', 
      name: 'John Deere Tractor', 
      type: 'Tractor', 
      model: '8R 410', 
      status: 'online',
      bluetoothId: 'JD:8R:410:1234'
    },
    { 
      id: '2', 
      name: 'Case IH Combine', 
      type: 'Harvester', 
      model: 'Axial-Flow 250', 
      status: 'offline',
      lastConnected: '2023-10-15 14:30'
    },
    { 
      id: '3', 
      name: 'Sprayer System', 
      type: 'Sprayer', 
      model: 'R4060', 
      status: 'warning',
      bluetoothId: 'SPR:R4060:5678'
    },
    { 
      id: '4', 
      name: 'Irrigation Controller', 
      type: 'Irrigation', 
      model: 'SmartFlow 200', 
      status: 'error',
      bluetoothId: 'IRR:SF200:9012'
    }
  ]);
  const [selectedEquipment, setSelectedEquipment] = useState<Equipment | null>(null);
  const [diagnosticResults, setDiagnosticResults] = useState<DiagnosticResult[]>([]);
  const [runningDiagnostic, setRunningDiagnostic] = useState(false);
  const [diagnosticProgress, setDiagnosticProgress] = useState(0);
  const [connected, setConnected] = useState(false);

  useEffect(() => {
    // Simulate checking Bluetooth status
    setTimeout(() => {
      setBluetoothEnabled(true);
      setLoading(false);
    }, 1000);
  }, []);

  const startScan = () => {
    setScanning(true);
    
    // Simulate scanning for nearby equipment
    setTimeout(() => {
      setScanning(false);
      Alert.alert(
        'Scan Complete',
        'Found 4 pieces of equipment nearby.',
        [{ text: 'OK' }]
      );
    }, 2000);
  };

  const connectToEquipment = (item: Equipment) => {
    if (item.status === 'offline') {
      Alert.alert(
        'Connection Failed',
        `${item.name} is currently offline. Please ensure the equipment is powered on and within range.`,
        [{ text: 'OK' }]
      );
      return;
    }
    
    setSelectedEquipment(item);
    setConnected(false);
    setDiagnosticResults([]);
    
    // Simulate connection process
    setTimeout(() => {
      setConnected(true);
      Alert.alert(
        'Connected',
        `Successfully connected to ${item.name}.`,
        [{ text: 'OK' }]
      );
    }, 1500);
  };

  const disconnectEquipment = () => {
    setSelectedEquipment(null);
    setConnected(false);
    setDiagnosticResults([]);
  };

  const runDiagnostics = () => {
    if (!connected || !selectedEquipment) {
      Alert.alert('Error', 'Please connect to equipment first.');
      return;
    }
    
    setRunningDiagnostic(true);
    setDiagnosticProgress(0);
    setDiagnosticResults([]);
    
    // Simulate diagnostic progress
    const interval = setInterval(() => {
      setDiagnosticProgress(prev => {
        if (prev >= 100) {
          clearInterval(interval);
          setRunningDiagnostic(false);
          
          // Generate mock diagnostic results
          const results: DiagnosticResult[] = [
            { id: '1', name: 'Engine Temperature', status: 'normal', value: '85', unit: '°C', description: 'Engine temperature is within normal operating range.' },
            { id: '2', name: 'Oil Pressure', status: 'normal', value: '65', unit: 'psi', description: 'Oil pressure is optimal.' },
            { id: '3', name: 'Fuel Level', status: 'warning', value: '15', unit: '%', description: 'Fuel level is low. Consider refueling soon.' },
            { id: '4', name: 'Battery Voltage', status: 'normal', value: '12.7', unit: 'V', description: 'Battery is charging properly.' }
          ];
          
          setDiagnosticResults(results);
          return 0;
        }
        return prev + 10;
      });
    }, 500);
  };

  const getStatusColor = (status: 'online' | 'offline' | 'warning' | 'error'): string => {
    switch (status) {
      case 'online': return '#10b981';
      case 'offline': return '#6b7280';
      case 'warning': return '#f59e0b';
      case 'error': return '#ef4444';
      default: return '#6b7280';
    }
  };

  const getDiagnosticStatusColor = (status: 'normal' | 'warning' | 'error'): string => {
    switch (status) {
      case 'normal': return '#10b981';
      case 'warning': return '#f59e0b';
      case 'error': return '#ef4444';
      default: return '#6b7280';
    }
  };

  if (loading) {
    return (
      <SafeAreaView style={styles.loadingContainer}>
        <ActivityIndicator size="large" color="#4f46e5" />
        <Text style={styles.loadingText}>Loading equipment diagnostics...</Text>
      </SafeAreaView>
    );
  }

  if (bluetoothEnabled === false) {
    return (
      <SafeAreaView style={styles.container}>
        <StatusBar style="dark" />
        
        <View style={styles.header}>
          <Text style={styles.title}>Equipment Diagnostics</Text>
        </View>
        
        <View style={styles.bluetoothWarning}>
          <Ionicons name="bluetooth-off" size={48} color="#f59e0b" />
          <Text style={styles.bluetoothWarningTitle}>Bluetooth Required</Text>
          <Text style={styles.bluetoothWarningText}>
            Please enable Bluetooth on your device to connect to equipment for diagnostics.
          </Text>
          <TouchableOpacity
            style={styles.bluetoothButton}
            onPress={() => {
              // In a real app, this would open device settings
              Alert.alert('Enable Bluetooth', 'Please enable Bluetooth in your device settings.');
            }}
          >
            <Text style={styles.bluetoothButtonText}>Enable Bluetooth</Text>
          </TouchableOpacity>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar style="dark" />
      
      <View style={styles.header}>
        <Text style={styles.title}>Equipment Diagnostics</Text>
        <Text style={styles.subtitle}>
          Connect to equipment via Bluetooth for real-time diagnostics and troubleshooting.
        </Text>
      </View>

      {selectedEquipment ? (
        // Equipment detail view with diagnostics
        <View style={styles.detailContainer}>
          <View style={styles.equipmentHeader}>
            <View style={styles.equipmentInfo}>
              <Text style={styles.equipmentName}>{selectedEquipment.name}</Text>
              <Text style={styles.equipmentModel}>{selectedEquipment.type} • {selectedEquipment.model}</Text>
              <View style={styles.statusBadge}>
                <View style={[styles.statusDot, { backgroundColor: getStatusColor(selectedEquipment.status) }]} />
                <Text style={styles.statusText}>
                  {connected ? 'Connected' : selectedEquipment.status.charAt(0).toUpperCase() + selectedEquipment.status.slice(1)}
                </Text>
              </View>
            </View>
            
            <TouchableOpacity
              style={styles.disconnectButton}
              onPress={disconnectEquipment}
            >
              <Ionicons name="close-circle" size={24} color="#ef4444" />
            </TouchableOpacity>
          </View>
          
          {connected ? (
            <ScrollView style={styles.diagnosticsContainer}>
              {runningDiagnostic ? (
                <View style={styles.diagnosticProgress}>
                  <Text style={styles.diagnosticProgressText}>Running diagnostics...</Text>
                  <View style={styles.progressBarContainer}>
                    <View style={[styles.progressBar, { width: `${diagnosticProgress}%` }]} />
                  </View>
                  <Text style={styles.progressText}>{diagnosticProgress}% complete</Text>
                </View>
              ) : (
                <>
                  <TouchableOpacity
                    style={styles.runDiagnosticsButton}
                    onPress={runDiagnostics}
                  >
                    <Ionicons name="pulse" size={20} color="#ffffff" />
                    <Text style={styles.runDiagnosticsText}>Run Diagnostics</Text>
                  </TouchableOpacity>
                  
                  {diagnosticResults.length > 0 ? (
                    <View style={styles.resultsContainer}>
                      <Text style={styles.resultsTitle}>Diagnostic Results</Text>
                      
                      {diagnosticResults.map((result) => (
                        <View key={result.id} style={styles.resultItem}>
                          <View style={styles.resultHeader}>
                            <Text style={styles.resultName}>{result.name}</Text>
                            <View style={[styles.resultStatusBadge, { backgroundColor: getDiagnosticStatusColor(result.status) + '20' }]}>
                              <Text style={[styles.resultStatusText, { color: getDiagnosticStatusColor(result.status) }]}>
                                {result.status.toUpperCase()}
                              </Text>
                            </View>
                          </View>
                          
                          <View style={styles.resultValue}>
                            <Text style={[styles.resultValueText, { color: getDiagnosticStatusColor(result.status) }]}>
                              {result.value}{result.unit ? ` ${result.unit}` : ''}
                            </Text>
                          </View>
                          
                          {result.description && (
                            <Text style={styles.resultDescription}>{result.description}</Text>
                          )}
                        </View>
                      ))}
                    </View>
                  ) : (
                    <View style={styles.noResultsContainer}>
                      <Ionicons name="analytics-outline" size={48} color="#d1d5db" />
                      <Text style={styles.noResultsText}>No diagnostic data available</Text>
                      <Text style={styles.noResultsSubtext}>Run diagnostics to check equipment status</Text>
                    </View>
                  )}
                </>
              )}
            </ScrollView>
          ) : (
            <View style={styles.connectingContainer}>
              <ActivityIndicator size="large" color="#4f46e5" />
              <Text style={styles.connectingText}>Connecting to {selectedEquipment.name}...</Text>
              <Text style={styles.connectingSubtext}>Please keep your device close to the equipment</Text>
            </View>
          )}
        </View>
      ) : (
        // Equipment list view
        <>
          <View style={styles.scanContainer}>
            <TouchableOpacity
              style={styles.scanButton}
              onPress={startScan}
              disabled={scanning}
            >
              <Ionicons name="bluetooth" size={20} color="#ffffff" />
              <Text style={styles.scanButtonText}>
                {scanning ? 'Scanning...' : 'Scan for Equipment'}
              </Text>
            </TouchableOpacity>
            
            {scanning && (
              <ActivityIndicator style={styles.scanningIndicator} color="#4f46e5" />
            )}
          </View>
          
          <View style={styles.listContainer}>
            <Text style={styles.listTitle}>Available Equipment</Text>
            
            <FlatList
              data={equipment}
              keyExtractor={(item) => item.id}
              renderItem={({ item }) => (
                <TouchableOpacity
                  style={styles.equipmentCard}
                  onPress={() => connectToEquipment(item)}
                >
                  <View style={styles.equipmentCardContent}>
                    <View style={[styles.equipmentIcon, { backgroundColor: getStatusColor(item.status) + '20' }]}>
                      {item.type === 'Tractor' && <Ionicons name="car" size={24} color={getStatusColor(item.status)} />}
                      {item.type === 'Harvester' && <Ionicons name="cut" size={24} color={getStatusColor(item.status)} />}
                      {item.type === 'Sprayer' && <Ionicons name="water" size={24} color={getStatusColor(item.status)} />}
                      {item.type === 'Irrigation' && <Ionicons name="rainy" size={24} color={getStatusColor(item.status)} />}
                    </View>
                    
                    <View style={styles.equipmentCardDetails}>
                      <Text style={styles.equipmentCardName}>{item.name}</Text>
                      <Text style={styles.equipmentCardModel}>{item.model}</Text>
                      <View style={styles.equipmentCardStatus}>
                        <View style={[styles.statusDot, { backgroundColor: getStatusColor(item.status) }]} />
                        <Text style={styles.equipmentCardStatusText}>
                          {item.status.charAt(0).toUpperCase() + item.status.slice(1)}
                          {item.status === 'offline' && item.lastConnected && ` • Last seen: ${item.lastConnected}`}
                        </Text>
                      </View>
                    </View>
                    
                    <Ionicons name="chevron-forward" size={20} color="#9ca3af" />
                  </View>
                </TouchableOpacity>
              )}
              contentContainerStyle={styles.equipmentList}
            />
          </View>
        </>
      )}
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f9fafb',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#f9fafb',
  },
  loadingText: {
    marginTop: 10,
    fontSize: 16,
    color: '#4b5563',
  },
  header: {
    padding: 16,
    backgroundColor: '#ffffff',
    borderBottomWidth: 1,
    borderBottomColor: '#e5e7eb',
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#111827',
  },
  subtitle: {
    fontSize: 14,
    color: '#6b7280',
    marginTop: 4,
  },
  bluetoothWarning: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 24,
  },
  bluetoothWarningTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#111827',
    marginTop: 16,
    marginBottom: 8,
  },
  bluetoothWarningText: {
    fontSize: 16,
    color: '#6b7280',
    textAlign: 'center',
    marginBottom: 24,
  },
  bluetoothButton: {
    backgroundColor: '#4f46e5',
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 8,
  },
  bluetoothButtonText: {
    color: '#ffffff',
    fontSize: 16,
    fontWeight: '600',
  },
  scanContainer: {
    padding: 16,
    flexDirection: 'row',
    alignItems: 'center',
  },
  scanButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#4f46e5',
    paddingHorizontal: 16,
    paddingVertical: 10,
    borderRadius: 8,
  },
  scanButtonText: {
    color: '#ffffff',
    fontSize: 16,
    fontWeight: '500',
    marginLeft: 8,
  },
  scanningIndicator: {
    marginLeft: 16,
  },
  listContainer: {
    flex: 1,
    paddingHorizontal: 16,
  },
  listTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#111827',
    marginBottom: 12,
  },
  equipmentList: {
    paddingBottom: 16,
  },
  equipmentCard: {
    backgroundColor: '#ffffff',
    borderRadius: 8,
    marginBottom: 12,
    borderWidth: 1,
    borderColor: '#e5e7eb',
    overflow: 'hidden',
  },
  equipmentCardContent: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
  },
  equipmentIcon: {
    width: 48,
    height: 48,
    borderRadius: 24,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 16,
  },
  equipmentCardDetails: {
    flex: 1,
  },
  equipmentCardName: {
    fontSize: 16,
    fontWeight: '600',
    color: '#111827',
  },
  equipmentCardModel: {
    fontSize: 14,
    color: '#6b7280',
    marginTop: 2,
  },
  equipmentCardStatus: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 4,
  },
  statusDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    marginRight: 6,
  },
  equipmentCardStatusText: {
    fontSize: 12,
    color: '#6b7280',
  },
  detailContainer: {
    flex: 1,
    padding: 16,
  },
  equipmentHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    backgroundColor: '#ffffff',
    borderRadius: 8,
    padding: 16,
    borderWidth: 1,
    borderColor: '#e5e7eb',
    marginBottom: 16,
  },
  equipmentInfo: {
    flex: 1,
  },
  equipmentName: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#111827',
  },
  equipmentModel: {
    fontSize: 14,
    color: '#6b7280',
    marginTop: 2,
  },
  statusBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 8,
  },
  statusText: {
    fontSize: 14,
    color: '#6b7280',
  },
  disconnectButton: {
    padding: 4,
  },
  connectingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#ffffff',
    borderRadius: 8,
    padding: 24,
    borderWidth: 1,
    borderColor: '#e5e7eb',
  },
  connectingText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#111827',
    marginTop: 16,
    marginBottom: 8,
  },
  connectingSubtext: {
    fontSize: 14,
    color: '#6b7280',
    textAlign: 'center',
  },
  diagnosticsContainer: {
    flex: 1,
    backgroundColor: '#ffffff',
    borderRadius: 8,
    padding: 16,
    borderWidth: 1,
    borderColor: '#e5e7eb',
  },
  runDiagnosticsButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#4f46e5',
    paddingVertical: 12,
    borderRadius: 8,
    marginBottom: 16,
  },
  runDiagnosticsText: {
    color: '#ffffff',
    fontSize: 16,
    fontWeight: '600',
    marginLeft: 8,
  },
  diagnosticProgress: {
    alignItems: 'center',
    padding: 16,
  },
  diagnosticProgressText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#111827',
    marginBottom: 16,
  },
  progressBarContainer: {
    width: '100%',
    height: 8,
    backgroundColor: '#e5e7eb',
    borderRadius: 4,
    overflow: 'hidden',
    marginBottom: 8,
  },
  progressBar: {
    height: '100%',
    backgroundColor: '#4f46e5',
  },
  progressText: {
    fontSize: 14,
    color: '#6b7280',
  },
  resultsContainer: {
    marginTop: 8,
  },
  resultsTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#111827',
    marginBottom: 12,
  },
  resultItem: {
    backgroundColor: '#f9fafb',
    borderRadius: 8,
    padding: 12,
    marginBottom: 8,
    borderWidth: 1,
    borderColor: '#e5e7eb',
  },
  resultHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  resultName: {
    fontSize: 16,
    fontWeight: '600',
    color: '#111827',
  },
  resultStatusBadge: {
    paddingHorizontal: 8,
    paddingVertical: 2,
    borderRadius: 4,
  },
  resultStatusText: {
    fontSize: 12,
    fontWeight: '600',
  },
  resultValue: {
    marginBottom: 8,
  },
  resultValueText: {
    fontSize: 20,
    fontWeight: 'bold',
  },
  resultDescription: {
    fontSize: 14,
    color: '#6b7280',
  },
  noResultsContainer: {
    alignItems: 'center',
    padding: 24,
  },
  noResultsText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#111827',
    marginTop: 16,
    marginBottom: 4,
  },
  noResultsSubtext: {
    fontSize: 14,
    color: '#6b7280',
    textAlign: 'center',
  }
});

export default EquipmentDiagnosticsScreen;