import db from '../db.js';
import { v4 as uuidv4 } from 'uuid';

/**
 * Model for user online status
 */
class UserOnlineStatus {
  /**
   * Set a user as online
   * @param {string} userId - User ID
   * @returns {Promise<Object>} Updated status
   */
  static async setOnline(userId) {
    try {
      // Check if status record exists
      const existingStatus = await this.getByUserId(userId);

      if (existingStatus) {
        // Update existing status
        const query = `
          UPDATE user_online_status
          SET is_online = TRUE, last_active_at = CURRENT_TIMESTAMP
          WHERE user_id = $1
          RETURNING *
        `;

        const result = await db.query(query, [userId]);
        return result.rows[0];
      }

      // Create new status
      const id = uuidv4();
      const query = `
        INSERT INTO user_online_status (id, user_id, is_online, last_active_at)
        VALUES ($1, $2, TRUE, CURRENT_TIMESTAMP)
        RETURNING *
      `;

      const result = await db.query(query, [id, userId]);
      return result.rows[0];
    } catch (error) {
      // Check if the error is due to the table not existing
      if (error.message.includes('relation "user_online_status" does not exist')) {
        console.error('Error: user_online_status table does not exist. Please run the migration to create it.');
        return null;
      }
      // Re-throw other errors
      throw error;
    }
  }

  /**
   * Set a user as offline
   * @param {string} userId - User ID
   * @returns {Promise<Object>} Updated status
   */
  static async setOffline(userId) {
    try {
      // Check if status record exists
      const existingStatus = await this.getByUserId(userId);

      if (!existingStatus) {
        // No status record exists, create one with offline status
        const id = uuidv4();
        const query = `
          INSERT INTO user_online_status (id, user_id, is_online, last_active_at)
          VALUES ($1, $2, FALSE, CURRENT_TIMESTAMP)
          RETURNING *
        `;

        const result = await db.query(query, [id, userId]);
        return result.rows[0];
      }

      const query = `
        UPDATE user_online_status
        SET is_online = FALSE, last_active_at = CURRENT_TIMESTAMP
        WHERE user_id = $1
        RETURNING *
      `;

      const result = await db.query(query, [userId]);
      return result.rows[0];
    } catch (error) {
      // Check if the error is due to the table not existing
      if (error.message.includes('relation "user_online_status" does not exist')) {
        console.error('Error: user_online_status table does not exist. Please run the migration to create it.');
        return null;
      }
      // Re-throw other errors
      throw error;
    }
  }

  /**
   * Update a user's last active timestamp
   * @param {string} userId - User ID
   * @returns {Promise<Object>} Updated status
   */
  static async updateLastActive(userId) {
    try {
      // Check if status record exists
      const existingStatus = await this.getByUserId(userId);

      if (!existingStatus) {
        // No status record exists, create one
        return this.setOnline(userId);
      }

      const query = `
        UPDATE user_online_status
        SET last_active_at = CURRENT_TIMESTAMP
        WHERE user_id = $1
        RETURNING *
      `;

      const result = await db.query(query, [userId]);
      return result.rows[0];
    } catch (error) {
      // Check if the error is due to the table not existing
      if (error.message.includes('relation "user_online_status" does not exist')) {
        console.error('Error: user_online_status table does not exist. Please run the migration to create it.');
        return null;
      }
      // Re-throw other errors
      throw error;
    }
  }

  /**
   * Get online status for a user
   * @param {string} userId - User ID
   * @returns {Promise<Object>} Status
   */
  static async getByUserId(userId) {
    try {
      const query = `
        SELECT * FROM user_online_status
        WHERE user_id = $1
      `;

      const result = await db.query(query, [userId]);
      return result.rows[0];
    } catch (error) {
      // Check if the error is due to the table not existing
      if (error.message.includes('relation "user_online_status" does not exist')) {
        console.error('Error: user_online_status table does not exist. Please run the migration to create it.');
        return null;
      }
      // Re-throw other errors
      throw error;
    }
  }

  /**
   * Get online status for multiple users
   * @param {Array<string>} userIds - User IDs
   * @returns {Promise<Array>} Statuses
   */
  static async getByUserIds(userIds) {
    if (!userIds.length) {
      return [];
    }

    try {
      // Create parameterized query with the correct number of parameters
      const params = userIds.map((_, index) => `$${index + 1}`).join(', ');

      const query = `
        SELECT uos.*, u.first_name, u.last_name, u.email
        FROM user_online_status uos
        JOIN users u ON uos.user_id = u.id
        WHERE uos.user_id IN (${params})
      `;

      const result = await db.query(query, userIds);
      return result.rows;
    } catch (error) {
      // Check if the error is due to the table not existing
      if (error.message.includes('relation "user_online_status" does not exist')) {
        console.error('Error: user_online_status table does not exist. Please run the migration to create it.');
        return [];
      }
      // Re-throw other errors
      throw error;
    }
  }

  /**
   * Get all online users
   * @returns {Promise<Array>} Online users
   */
  static async getAllOnline() {
    try {
      const query = `
        SELECT uos.*, u.first_name, u.last_name, u.email
        FROM user_online_status uos
        JOIN users u ON uos.user_id = u.id
        WHERE uos.is_online = TRUE
        ORDER BY uos.last_active_at DESC
      `;

      const result = await db.query(query);
      return result.rows;
    } catch (error) {
      // Check if the error is due to the table not existing
      if (error.message.includes('relation "user_online_status" does not exist')) {
        console.error('Error: user_online_status table does not exist. Please run the migration to create it.');
        return [];
      }
      // Re-throw other errors
      throw error;
    }
  }

  /**
   * Get all online users for a farm
   * @param {string} farmId - Farm ID
   * @returns {Promise<Array>} Online users
   */
  static async getOnlineByFarmId(farmId) {
    try {
      const query = `
        SELECT uos.*, u.first_name, u.last_name, u.email
        FROM user_online_status uos
        JOIN users u ON uos.user_id = u.id
        JOIN user_farms uf ON u.id = uf.user_id
        WHERE uos.is_online = TRUE
        AND uf.farm_id = $1
        ORDER BY uos.last_active_at DESC
      `;

      const result = await db.query(query, [farmId]);
      return result.rows;
    } catch (error) {
      // Check if the error is due to the table not existing
      if (error.message.includes('relation "user_online_status" does not exist')) {
        console.error('Error: user_online_status table does not exist. Please run the migration to create it.');
        return [];
      }
      // Re-throw other errors
      throw error;
    }
  }

  /**
   * Check if a user is online
   * @param {string} userId - User ID
   * @returns {Promise<boolean>} Is online
   */
  static async isOnline(userId) {
    try {
      const status = await this.getByUserId(userId);
      return status ? status.is_online : false;
    } catch (error) {
      console.error(`Error checking if user ${userId} is online:`, error.message);
      return false;
    }
  }

  /**
   * Get time since user was last active
   * @param {string} userId - User ID
   * @returns {Promise<number>} Seconds since last active
   */
  static async getTimeSinceLastActive(userId) {
    try {
      const query = `
        SELECT EXTRACT(EPOCH FROM (CURRENT_TIMESTAMP - last_active_at)) as seconds_since_active
        FROM user_online_status
        WHERE user_id = $1
      `;

      const result = await db.query(query, [userId]);

      if (result.rowCount === 0) {
        return null;
      }

      return parseInt(result.rows[0].seconds_since_active, 10);
    } catch (error) {
      // Check if the error is due to the table not existing
      if (error.message.includes('relation "user_online_status" does not exist')) {
        console.error('Error: user_online_status table does not exist. Please run the migration to create it.');
        return null;
      }
      // Re-throw other errors
      throw error;
    }
  }

  /**
   * Mark users as offline if they haven't been active for a certain time
   * @param {number} inactiveSeconds - Seconds of inactivity to consider offline
   * @returns {Promise<number>} Number of users marked offline
   */
  static async markInactiveUsersOffline(inactiveSeconds = 300) { // Default 5 minutes
    try {
      const query = `
        UPDATE user_online_status
        SET is_online = FALSE
        WHERE is_online = TRUE
        AND EXTRACT(EPOCH FROM (CURRENT_TIMESTAMP - last_active_at)) > $1
        RETURNING user_id
      `;

      const result = await db.query(query, [inactiveSeconds]);
      return result.rowCount;
    } catch (error) {
      // Check if the error is due to the table not existing
      if (error.message.includes('relation "user_online_status" does not exist')) {
        console.error('Error: user_online_status table does not exist. Please run the migration to create it.');
        // Return 0 to indicate no users were marked offline
        return 0;
      }
      // Re-throw other errors
      throw error;
    }
  }

  /**
   * Delete old status records
   * @param {number} olderThanDays - Days to consider a record old
   * @returns {Promise<number>} Number of records deleted
   */
  static async cleanupOldRecords(olderThanDays = 30) {
    try {
      const query = `
        DELETE FROM user_online_status
        WHERE last_active_at < CURRENT_TIMESTAMP - INTERVAL '${olderThanDays} days'
        RETURNING user_id
      `;

      const result = await db.query(query);
      return result.rowCount;
    } catch (error) {
      // Check if the error is due to the table not existing
      if (error.message.includes('relation "user_online_status" does not exist')) {
        console.error('Error: user_online_status table does not exist. Please run the migration to create it.');
        return 0;
      }
      // Re-throw other errors
      throw error;
    }
  }
}

export default UserOnlineStatus;
