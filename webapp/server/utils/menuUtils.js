import MenuPreference from '../models/MenuPreference.js';
import User from '../models/User.js';

/**
 * NOTE: This file is the server-side version of menuUtils.ts in the src/utils directory.
 * Both files should be kept in sync with each other.
 * This server-side version contains additional functions for database operations.
 * The client-side version (menuUtils.ts) provides TypeScript types and the default menu structure.
 */

/**
 * Returns the default menu structure for the application
 * This is the single source of truth for the menu structure
 * @returns {object} - The default menu structure
 */
export const getDefaultMenuStructure = () => {
  // Header items - simplified to only show dashboard and admin by default
  // Note: Quick Actions dropdown is hardcoded in the Header component
  const headerItems = [
    // Core Operations - always visible by default
    { id: 'dashboard', title: 'Dashboard', path: '/dashboard', category: 'header', isRequired: true, isVisible: true },

    // Admin - only visible for global admin roles, handled in MenuPreferencesContext
    { id: 'admin-header', title: 'Admin', path: '/admin', category: 'header', isRequired: false, isVisible: false },

    // Other items - hidden by default, can be enabled by user
    { id: 'tasks', title: 'Tasks', path: '/tasks', category: 'header', isRequired: false, isVisible: false },
    { id: 'weather-header', title: 'Weather', path: '/weather', category: 'header', isRequired: false, isVisible: false },
    { id: 'market-prices-header', title: 'Market Prices', path: '/market-prices', category: 'header', isRequired: false, isVisible: false },
    { id: 'sustainability-header', title: 'Sustainability', path: '/sustainability', category: 'header', isRequired: false, isVisible: false },
    { id: 'hr', title: 'HR', path: '/hr', category: 'header', isRequired: false, isVisible: false },
    { id: 'billing-header', title: 'Billing', path: '/billing', category: 'header', isRequired: false, isVisible: false },
    { id: 'labor-header', title: 'Labor', path: '/labor', category: 'header', isRequired: false, isVisible: false },
    { id: 'suppliers-header', title: 'Suppliers', path: '/suppliers', category: 'header', isRequired: false, isVisible: false },
    { id: 'transport-header', title: 'Transport', path: '/transport', category: 'header', isRequired: false, isVisible: false },
    { id: 'drivers-header', title: 'Drivers', path: '/transport/drivers', category: 'header', isRequired: false, isVisible: false },
    { id: 'market-header', title: 'Market', path: '/market', category: 'header', isRequired: false, isVisible: false },
    { id: 'ai-assistant-header', title: 'AI Assistant', path: '/ai-assistant', category: 'header', isRequired: false, isVisible: false },
    { id: 'ai-decision-support-header', title: 'AI Decision Support', path: '/ai-assistant?tab=decision-support', category: 'header', isRequired: false, isVisible: false },
  ];

  // Mock sidebar categories - reorganized with logical grouping
  const sidebarCategories = [
    // Core Farm Management
    {
      id: 'dashboard-analytics',
      title: 'Dashboard & Analytics',
      items: [
        { id: 'dashboard-sidebar', title: 'Dashboard', path: '/dashboard', category: 'dashboard-analytics', isRequired: false, isVisible: true },
        { id: 'reports', title: 'Reports', path: '/reports', category: 'dashboard-analytics', isRequired: false, isVisible: true },
        { id: 'market-prices', title: 'Market Price Tracker', path: '/market-prices', category: 'dashboard-analytics', isRequired: false, isVisible: true },
        { id: 'field-health', title: 'Field Health Analytics', path: '/field-health', category: 'dashboard-analytics', isRequired: false, isVisible: true },
      ]
    },
    {
      id: 'farm-operations',
      title: 'Farm Operations',
      items: [
        { id: 'fields', title: 'Fields', path: '/fields', category: 'farm-operations', isRequired: false, isVisible: true },
        { id: 'crops', title: 'Crops', path: '/crops', category: 'farm-operations', isRequired: false, isVisible: true },
        { id: 'crop-types', title: 'Crop Types', path: '/crop-types', category: 'farm-operations', isRequired: false, isVisible: true },
        { id: 'crop-disease-prediction', title: 'Crop Disease Prediction', path: '/crop-management/disease-prediction', category: 'farm-operations', isRequired: false, isVisible: true },
        { id: 'yield-prediction', title: 'Yield Prediction', path: '/crop-management/yield-prediction', category: 'farm-operations', isRequired: false, isVisible: true },
        { id: 'crop-rotation', title: 'Crop Rotation Optimization', path: '/crop-management/rotation-optimization', category: 'farm-operations', isRequired: false, isVisible: true },
        { id: 'harvest-scheduling', title: 'Harvest Scheduling', path: '/crop-management/harvest-scheduling', category: 'farm-operations', isRequired: false, isVisible: true },
        { id: 'livestock', title: 'Livestock', path: '/livestock', category: 'farm-operations', isRequired: false, isVisible: true },
        { id: 'soil', title: 'Soil Management', path: '/soil', category: 'farm-operations', isRequired: false, isVisible: true },
        { id: 'vets', title: 'Veterinary Management', path: '/vets', category: 'farm-operations', isRequired: false, isVisible: true },
        { id: 'weather', title: 'Weather System', path: '/weather', category: 'farm-operations', isRequired: false, isVisible: true },
      ]
    },
    {
      id: 'tasks-workflows',
      title: 'Tasks & Workflows',
      items: [
        { id: 'tasks-sidebar', title: 'Tasks', path: '/tasks', category: 'tasks-workflows', isRequired: false, isVisible: true },
        { id: 'workflows', title: 'Workflow Automation', path: '/workflows', category: 'tasks-workflows', isRequired: false, isVisible: true },
      ]
    },

    // Financial & Business Operations
    {
      id: 'financial-management',
      title: 'Financial Management',
      items: [
        { id: 'transactions', title: 'Transactions', path: '/transactions', category: 'financial-management', isRequired: false, isVisible: true },
        { id: 'link-account', title: 'Link Account', path: '/link-account', category: 'financial-management', isRequired: false, isVisible: true },
        { id: 'farms', title: 'Farms', path: '/farms', category: 'financial-management', isRequired: false, isVisible: true },
        { id: 'customers', title: 'Customers', path: '/customers', category: 'financial-management', isRequired: false, isVisible: true },
        { id: 'invoices', title: 'Invoices', path: '/invoices', category: 'financial-management', isRequired: false, isVisible: true },
        { id: 'receipts', title: 'Receipts', path: '/receipts', category: 'financial-management', isRequired: false, isVisible: true },
        { id: 'products', title: 'Products', path: '/products', category: 'financial-management', isRequired: false, isVisible: true },
        { id: 'tax-management', title: 'Tax Management', path: '/financial-management/tax', category: 'financial-management', isRequired: false, isVisible: true },
      ]
    },
    {
      id: 'hr-management',
      title: 'HR Management',
      items: [
        { id: 'hr-dashboard', title: 'HR Dashboard', path: '/hr', category: 'hr-management', isRequired: false, isVisible: true },
        { id: 'time-entries', title: 'Time Entries', path: '/hr/time-entries', category: 'hr-management', isRequired: false, isVisible: true },
        { id: 'time-off-requests', title: 'Time Off Requests', path: '/hr/time-off-requests', category: 'hr-management', isRequired: false, isVisible: true },
        { id: 'pay-stubs', title: 'Pay Stubs', path: '/hr/pay-stubs', category: 'hr-management', isRequired: false, isVisible: true },
        { id: 'expenses', title: 'Expenses', path: '/hr/expenses', category: 'hr-management', isRequired: false, isVisible: true },
      ]
    },
    {
      id: 'labor-management',
      title: 'Labor Management',
      items: [
        { id: 'labor-dashboard', title: 'Labor Dashboard', path: '/labor', category: 'labor-management', isRequired: false, isVisible: true },
        { id: 'seasonal-workers', title: 'Seasonal Workers', path: '/labor/seasonal-workers', category: 'labor-management', isRequired: false, isVisible: true },
        { id: 'labor-cost-analysis', title: 'Labor Cost Analysis', path: '/labor/cost-analysis', category: 'labor-management', isRequired: false, isVisible: true },
        { id: 'compliance-tracking', title: 'Compliance Tracking', path: '/labor/compliance', category: 'labor-management', isRequired: false, isVisible: true },
        { id: 'worker-certifications', title: 'Worker Certifications', path: '/labor/certifications', category: 'labor-management', isRequired: false, isVisible: true },
      ]
    },

    // Supply Chain & Resources
    {
      id: 'resource-management',
      title: 'Resource Management',
      items: [
        { id: 'inventory', title: 'Inventory', path: '/inventory', category: 'resource-management', isRequired: false, isVisible: true },
        { id: 'equipment', title: 'Equipment', path: '/equipment', category: 'resource-management', isRequired: false, isVisible: true },
        { id: 'equipment-sharing', title: 'Equipment Sharing', path: '/equipment-sharing', category: 'resource-management', isRequired: false, isVisible: true },
        { id: 'employees', title: 'Employees', path: '/employees', category: 'resource-management', isRequired: false, isVisible: true },
        { id: 'maintenance', title: 'Equipment Maintenance', path: '/maintenance', category: 'resource-management', isRequired: false, isVisible: true },
        { id: 'suppliers', title: 'Supplier Marketplace', path: '/suppliers', category: 'resource-management', isRequired: false, isVisible: true },
      ]
    },
    {
      id: 'transport-management',
      title: 'Transport Management',
      items: [
        { id: 'transport-dashboard', title: 'Transport Dashboard', path: '/transport', category: 'transport-management', isRequired: false, isVisible: true },
        { id: 'drivers', title: 'Drivers', path: '/transport/drivers', category: 'transport-management', isRequired: false, isVisible: true },
        { id: 'deliveries', title: 'Deliveries', path: '/transport/deliveries', category: 'transport-management', isRequired: false, isVisible: true },
        { id: 'pickups', title: 'Pickups', path: '/transport/pickups', category: 'transport-management', isRequired: false, isVisible: true },
        { id: 'driver-schedules', title: 'Driver Schedules', path: '/transport/schedules', category: 'transport-management', isRequired: false, isVisible: true },
        { id: 'driver-locations', title: 'Driver Locations', path: '/transport/locations', category: 'transport-management', isRequired: false, isVisible: true },
      ]
    },

    // Market & Sustainability
    {
      id: 'market-integration',
      title: 'Market Integration',
      items: [
        { id: 'market-dashboard', title: 'Market Dashboard', path: '/market', category: 'market-integration', isRequired: false, isVisible: true },
        { id: 'market-contracts', title: 'Contracts', path: '/market/contracts', category: 'market-integration', isRequired: false, isVisible: true },
        { id: 'price-comparison', title: 'Price Comparison', path: '/market/price-comparison', category: 'market-integration', isRequired: false, isVisible: true },
        { id: 'market-trends', title: 'Market Trends', path: '/market/trends', category: 'market-integration', isRequired: false, isVisible: true },
        { id: 'marketplace', title: 'Marketplace', path: '/market/marketplace', category: 'market-integration', isRequired: false, isVisible: true },
      ]
    },
    {
      id: 'sustainability-tracking',
      title: 'Sustainability Tracking',
      items: [
        { id: 'sustainability-dashboard', title: 'Sustainability Dashboard', path: '/sustainability', category: 'sustainability-tracking', isRequired: false, isVisible: true },
        { id: 'carbon-footprint', title: 'Carbon Footprint', path: '/sustainability/carbon-footprint', category: 'sustainability-tracking', isRequired: false, isVisible: true },
        { id: 'sustainable-practices', title: 'Sustainable Practices', path: '/sustainability/practices', category: 'sustainability-tracking', isRequired: false, isVisible: true },
        { id: 'certifications', title: 'Certification Management', path: '/sustainability/certifications', category: 'sustainability-tracking', isRequired: false, isVisible: true },
        { id: 'environmental-impact', title: 'Environmental Impact', path: '/sustainability/impact', category: 'sustainability-tracking', isRequired: false, isVisible: true },
      ]
    },

    // Tools & Settings
    {
      id: 'document-management',
      title: 'Document Management',
      items: [
        { id: 'documents', title: 'Documents', path: '/documents', category: 'document-management', isRequired: false, isVisible: true },
        { id: 'external-storage', title: 'External Storage', path: '/documents/external', category: 'document-management', isRequired: false, isVisible: true },
        { id: 'document-signing', title: 'Document Signing', path: '/documents/signing', category: 'document-management', isRequired: false, isVisible: true },
      ]
    },
    {
      id: 'ai-assistant',
      title: 'AI Assistant',
      items: [
        { id: 'ai-assistant-dashboard', title: 'AI Assistant', path: '/ai-assistant', category: 'ai-assistant', isRequired: false, isVisible: true },
        { id: 'ai-decision-support', title: 'Decision Support', path: '/ai-assistant?tab=decision-support', category: 'ai-assistant', isRequired: false, isVisible: true },
        { id: 'ai-predictive-maintenance', title: 'Predictive Maintenance', path: '/ai-assistant?tab=predictive-maintenance', category: 'ai-assistant', isRequired: false, isVisible: true },
        { id: 'ai-harvest-recommendations', title: 'Harvest Recommendations', path: '/ai-assistant?tab=harvest-recommendations', category: 'ai-assistant', isRequired: false, isVisible: true },
        { id: 'ai-field-improvement', title: 'Field Improvement', path: '/ai-assistant?tab=field-improvement', category: 'ai-assistant', isRequired: false, isVisible: true },
        { id: 'ai-financial-optimization', title: 'Financial Optimization', path: '/ai-assistant?tab=financial-optimization', category: 'ai-assistant', isRequired: false, isVisible: true },
        { id: 'ai-yield-profit', title: 'Yield & Profit', path: '/ai-assistant?tab=yield-profit', category: 'ai-assistant', isRequired: false, isVisible: true },
        { id: 'crop-disease-prediction', title: 'Crop Disease Prediction', path: '/crop-management/disease-prediction', category: 'ai-assistant', isRequired: false, isVisible: true },
        { id: 'crop-rotation-optimization', title: 'Crop Rotation Optimization', path: '/crop-management/rotation-optimization', category: 'ai-assistant', isRequired: false, isVisible: true },
        { id: 'yield-prediction', title: 'Yield Prediction', path: '/crop-management/yield-prediction', category: 'ai-assistant', isRequired: false, isVisible: true },
        { id: 'harvest-scheduling', title: 'Harvest Scheduling', path: '/crop-management/harvest-scheduling', category: 'ai-assistant', isRequired: false, isVisible: true },
        { id: 'receipt-ai-processing', title: 'Receipt AI Processing', path: '/receipts', category: 'ai-assistant', isRequired: false, isVisible: true },
      ]
    },
    {
      id: 'integrations-settings',
      title: 'Integrations & Settings',
      items: [
        { id: 'integrations', title: 'Integrations', path: '/integrations', category: 'integrations-settings', isRequired: false, isVisible: true },
        { id: 'iot', title: 'IoT Devices', path: '/iot', category: 'integrations-settings', isRequired: false, isVisible: true },
        { id: 'alerts', title: 'Alerts', path: '/alerts', category: 'integrations-settings', isRequired: false, isVisible: true },
        { id: 'data-migration', title: 'Data Migration', path: '/settings/data-migration', category: 'integrations-settings', isRequired: false, isVisible: true },
        { id: 'menu-customization', title: 'Menu Customization', path: '/settings/menu-customization', category: 'integrations-settings', isRequired: true, isVisible: true },
        { id: 'role-management', title: 'Role Management', path: '/settings/roles', category: 'integrations-settings', isRequired: false, isVisible: true },
        { id: 'grants', title: 'Agricultural Grants', path: '/grants', category: 'integrations-settings', isRequired: false, isVisible: true },
        { id: 'mobile-features', title: 'Mobile Features', path: '/mobile-features', category: 'integrations-settings', isRequired: false, isVisible: true },
        { id: 'password-manager', title: 'Password Manager', path: '/password-manager', category: 'integrations-settings', isRequired: false, isVisible: true },
      ]
    },
    {
      id: 'support',
      title: 'Support',
      items: [
        { id: 'support-dashboard', title: 'Support Dashboard', path: '/support', category: 'support', isRequired: false, isVisible: true },
        { id: 'support-tickets', title: 'Support Tickets', path: '/support/tickets', category: 'support', isRequired: false, isVisible: true },
        { id: 'create-support-ticket', title: 'Create Support Ticket', path: '/support/tickets/new', category: 'support', isRequired: false, isVisible: true },
      ]
    },
    {
      id: 'user-settings',
      title: 'User Settings',
      items: [
        { id: 'profile', title: 'Profile', path: '/profile', category: 'user-settings', isRequired: true, isVisible: true },
        { id: 'business-account', title: 'Business Account Management', path: '/business-account', category: 'user-settings', isRequired: false, isVisible: true },
        { id: 'faq', title: 'FAQ', path: '/faq', category: 'user-settings', isRequired: false, isVisible: true },
      ]
    }
  ];

  // Mock quick links items - reorganized with logical grouping
  const quickLinksItems = [
    // Farm Operations
    { id: 'fields-quick', title: 'Fields', path: '/fields', category: 'quick-links', isRequired: false, isVisible: true },
    { id: 'crop-types-quick', title: 'Crop Types', path: '/crop-types', category: 'quick-links', isRequired: false, isVisible: true },
    { id: 'weather-quick', title: 'Weather', path: '/weather', category: 'quick-links', isRequired: false, isVisible: true },
    { id: 'field-health-quick', title: 'Field Health', path: '/field-health', category: 'quick-links', isRequired: false, isVisible: true },

    // Financial & Resources
    { id: 'transactions-quick', title: 'Transactions', path: '/transactions', category: 'quick-links', isRequired: false, isVisible: true },
    { id: 'billing-quick', title: 'Billing System', path: '/billing', category: 'quick-links', isRequired: false, isVisible: true },
    { id: 'tax-management-quick', title: 'Tax Management', path: '/financial-management/tax', category: 'quick-links', isRequired: false, isVisible: true },
    { id: 'inventory-quick', title: 'Inventory', path: '/inventory', category: 'quick-links', isRequired: false, isVisible: true },
    { id: 'equipment-sharing-quick', title: 'Equipment Sharing', path: '/equipment-sharing', category: 'quick-links', isRequired: false, isVisible: true },

    // HR & Labor
    { id: 'hr-quick', title: 'HR Dashboard', path: '/hr', category: 'quick-links', isRequired: false, isVisible: true },
    { id: 'time-off-quick', title: 'Request Time Off', path: '/hr/time-off-requests/new', category: 'quick-links', isRequired: false, isVisible: true },
    { id: 'expense-quick', title: 'Submit Expense', path: '/hr/expenses/new', category: 'quick-links', isRequired: false, isVisible: true },
    { id: 'labor-quick', title: 'Labor Management', path: '/labor', category: 'quick-links', isRequired: false, isVisible: true },

    // Supply Chain
    { id: 'suppliers-quick', title: 'Supplier Marketplace', path: '/suppliers', category: 'quick-links', isRequired: false, isVisible: true },
    { id: 'transport-quick', title: 'Transport Management', path: '/transport', category: 'quick-links', isRequired: false, isVisible: true },

    // Market & Analytics
    { id: 'market-prices-quick', title: 'Market Prices', path: '/market-prices', category: 'quick-links', isRequired: false, isVisible: true },
    { id: 'market-quick', title: 'Market Integration', path: '/market', category: 'quick-links', isRequired: false, isVisible: true },
    { id: 'marketplace-quick', title: 'Marketplace', path: '/market/marketplace', category: 'quick-links', isRequired: false, isVisible: true },
    { id: 'market-trends-quick', title: 'Market Trends', path: '/market/trends', category: 'quick-links', isRequired: false, isVisible: true },

    // Sustainability & Tools
    { id: 'sustainability-quick', title: 'Sustainability Tracking', path: '/sustainability', category: 'quick-links', isRequired: false, isVisible: true },
    { id: 'reports-quick', title: 'Reports', path: '/reports', category: 'quick-links', isRequired: false, isVisible: true },
    { id: 'workflows-quick', title: 'Workflows', path: '/workflows', category: 'quick-links', isRequired: false, isVisible: true },
    { id: 'document-signing-quick', title: 'Document Signing', path: '/documents/signing', category: 'quick-links', isRequired: false, isVisible: true },
    { id: 'quickbooks-quick', title: 'QuickBooks', path: '/integrations/quickbooks/link', category: 'quick-links', isRequired: false, isVisible: true },

    // AI Features
    { id: 'ai-assistant-quick', title: 'AI Assistant', path: '/ai-assistant', category: 'quick-links', isRequired: false, isVisible: true },
    { id: 'ai-decision-support-quick', title: 'AI Decision Support', path: '/ai-assistant?tab=decision-support', category: 'quick-links', isRequired: false, isVisible: true },
    { id: 'ai-predictive-maintenance-quick', title: 'Predictive Maintenance', path: '/ai-assistant?tab=predictive-maintenance', category: 'quick-links', isRequired: false, isVisible: true },
    { id: 'crop-disease-prediction-quick', title: 'Crop Disease Prediction', path: '/crop-management/disease-prediction', category: 'quick-links', isRequired: false, isVisible: true },

    // Security Features
    { id: 'password-manager-quick', title: 'Password Manager', path: '/password-manager', category: 'quick-links', isRequired: false, isVisible: true },
  ];

  return {
    headerItems,
    sidebarCategories,
    quickLinksItems
  };
};

/**
 * Updates a user's menu preferences based on their role
 * @param {string} userId - The ID of the user
 * @param {object} role - The role object with name property
 * @param {object} transaction - Optional Sequelize transaction
 * @returns {Promise<boolean>} - True if successful, false if failed
 */
export const updateMenuPreferencesByRole = async (userId, role, transaction = null) => {
  try {
    console.log(`Updating menu preferences for user ${userId} based on role ${role.name}`);

    // Find the user's menu preferences
    const menuPreference = await MenuPreference.findOne({
      where: { user_id: userId },
      transaction
    });

    if (!menuPreference) {
      console.log(`No menu preferences found for user ${userId}`);
      return false;
    }

    // Get the current preferences
    const currentPreferences = menuPreference.preferences;

    // Add role-specific menu items
    const updatedPreferences = addRoleSpecificMenuItems(currentPreferences, role);

    // Update the menu preferences
    menuPreference.preferences = updatedPreferences;
    await menuPreference.save({ transaction });

    console.log(`Menu preferences updated successfully for user ${userId}`);
    return true;
  } catch (error) {
    console.error(`Error updating menu preferences for user ${userId}:`, error);
    return false;
  }
};

/**
 * Adds role-specific menu items to the user's menu preferences
 * @param {object} preferences - The current menu preferences
 * @param {object} role - The role object with name property
 * @returns {object} - The updated menu preferences
 */
const addRoleSpecificMenuItems = (preferences, role) => {
  // Make a deep copy of the preferences to avoid modifying the original
  const updatedPreferences = JSON.parse(JSON.stringify(preferences));

  // Get the role name in lowercase for comparison
  const roleName = role.name.toLowerCase();

  // Add role-specific menu items based on role level
  if (roleName === 'farm_owner' || roleName === 'farm_admin') {
    // Farm owners and admins get full access to all farm management features

    // Check if farm-management category already exists
    let farmManagementCategory = updatedPreferences.sidebarCategories.find(
      category => category.id === 'farm-management'
    );

    if (!farmManagementCategory) {
      // Add farm management category if it doesn't exist
      farmManagementCategory = {
        id: 'farm-management',
        title: 'Farm Management',
        items: []
      };
      updatedPreferences.sidebarCategories.push(farmManagementCategory);
    }

    // Add farm management items if they don't exist
    const farmManagementItems = [
      { id: 'farm-settings', title: 'Farm Settings', path: '/farm-settings', category: 'farm-management', isRequired: false, isVisible: true },
      { id: 'user-permissions', title: 'User Permissions', path: '/user-permissions', category: 'farm-management', isRequired: false, isVisible: true },
      { id: 'subscription', title: 'Subscription', path: '/subscription', category: 'farm-management', isRequired: false, isVisible: true },
      { id: 'document-signing', title: 'Document Signing', path: '/documents/signing', category: 'farm-management', isRequired: false, isVisible: true },
    ];

    farmManagementItems.forEach(item => {
      if (!farmManagementCategory.items.some(existingItem => existingItem.id === item.id)) {
        farmManagementCategory.items.push(item);
      }
    });

    // Add to quick links if it doesn't exist
    if (!updatedPreferences.quickLinksItems.some(item => item.id === 'farm-settings-quick')) {
      updatedPreferences.quickLinksItems.push(
        { id: 'farm-settings-quick', title: 'Farm Settings', path: '/farm-settings', category: 'quick-links', isRequired: false, isVisible: true }
      );
    }
  } 
  else if (roleName === 'farm_manager') {
    // Farm managers get access to operational features but not administrative ones

    // Check if team-management category already exists
    let teamManagementCategory = updatedPreferences.sidebarCategories.find(
      category => category.id === 'team-management'
    );

    if (!teamManagementCategory) {
      // Add team management category if it doesn't exist
      teamManagementCategory = {
        id: 'team-management',
        title: 'Team Management',
        items: []
      };
      updatedPreferences.sidebarCategories.push(teamManagementCategory);
    }

    // Add team management items if they don't exist
    const teamManagementItems = [
      { id: 'team-members', title: 'Team Members', path: '/team-members', category: 'team-management', isRequired: false, isVisible: true },
      { id: 'task-assignment', title: 'Task Assignment', path: '/task-assignment', category: 'team-management', isRequired: false, isVisible: true },
    ];

    teamManagementItems.forEach(item => {
      if (!teamManagementCategory.items.some(existingItem => existingItem.id === item.id)) {
        teamManagementCategory.items.push(item);
      }
    });

    // Add to quick links if it doesn't exist
    if (!updatedPreferences.quickLinksItems.some(item => item.id === 'task-assignment-quick')) {
      updatedPreferences.quickLinksItems.push(
        { id: 'task-assignment-quick', title: 'Task Assignment', path: '/task-assignment', category: 'quick-links', isRequired: false, isVisible: true }
      );
    }
  }
  else if (roleName === 'accountant') {
    // Accountants get specialized financial access

    // Check if accounting category already exists
    let accountingCategory = updatedPreferences.sidebarCategories.find(
      category => category.id === 'accounting'
    );

    if (!accountingCategory) {
      // Add accounting category if it doesn't exist
      accountingCategory = {
        id: 'accounting',
        title: 'Accounting',
        items: []
      };
      updatedPreferences.sidebarCategories.push(accountingCategory);
    }

    // Add accounting items if they don't exist
    const accountingItems = [
      { id: 'ledger', title: 'Ledger', path: '/ledger', category: 'accounting', isRequired: false, isVisible: true },
      { id: 'tax-management', title: 'Tax Management', path: '/financial-management/tax', category: 'accounting', isRequired: false, isVisible: true },
    ];

    accountingItems.forEach(item => {
      if (!accountingCategory.items.some(existingItem => existingItem.id === item.id)) {
        accountingCategory.items.push(item);
      }
    });

    // Add to quick links if it doesn't exist
    if (!updatedPreferences.quickLinksItems.some(item => item.id === 'ledger-quick')) {
      updatedPreferences.quickLinksItems.push(
        { id: 'ledger-quick', title: 'Ledger', path: '/ledger', category: 'quick-links', isRequired: false, isVisible: true }
      );
    }
  }
  // For farm_employee, no additional menus are added

  return updatedPreferences;
};

/**
 * Updates all users' menu preferences with new menu items from the default menu structure
 * @param {object} defaultMenuStructure - The default menu structure
 * @returns {Promise<number>} - Number of users updated
 */
export const updateAllUsersMenuPreferences = async (defaultMenuStructure) => {
  try {
    console.log('Updating all users menu preferences with new menu items');

    // Get all users
    const users = await User.findAll({
      include: [{
        model: MenuPreference,
        required: false
      }]
    });

    console.log(`Found ${users.length} users to update`);

    let updatedCount = 0;

    // For each user
    for (const user of users) {
      // Skip users without menu preferences
      if (!user.MenuPreference) {
        console.log(`User ${user.id} has no menu preferences, skipping`);
        continue;
      }

      // Get the user's current preferences
      const currentPreferences = user.MenuPreference.preferences;

      // Update the user's preferences with new menu items
      const updatedPreferences = mergeNewMenuItems(currentPreferences, defaultMenuStructure);

      // If preferences were updated, save them
      // Use JSON.stringify for deep comparison
      if (JSON.stringify(updatedPreferences) !== JSON.stringify(currentPreferences)) {
        user.MenuPreference.preferences = updatedPreferences;
        await user.MenuPreference.save();
        updatedCount++;
        console.log(`Updated menu preferences for user ${user.id}`);
      }
    }

    console.log(`Updated menu preferences for ${updatedCount} users`);
    return updatedCount;
  } catch (error) {
    console.error('Error updating all users menu preferences:', error);
    throw error;
  }
};

/**
 * Merges new menu items from the default menu structure into the user's preferences
 * @param {object} userPreferences - The user's current menu preferences
 * @param {object} defaultMenuStructure - The default menu structure
 * @returns {object} - The updated user preferences
 */
const mergeNewMenuItems = (userPreferences, defaultMenuStructure) => {
  // Make a deep copy of the user preferences to avoid modifying the original
  const updatedPreferences = JSON.parse(JSON.stringify(userPreferences));

  // Merge header items
  defaultMenuStructure.headerItems.forEach(defaultItem => {
    if (!updatedPreferences.headerItems.some(item => item.id === defaultItem.id)) {
      // Add the new item with isVisible set to true by default
      // This ensures new features are shown to users by default
      updatedPreferences.headerItems.push({
        ...defaultItem,
        isVisible: true
      });
    }
  });

  // Merge sidebar categories and their items
  defaultMenuStructure.sidebarCategories.forEach(defaultCategory => {
    // Find or create the category
    let category = updatedPreferences.sidebarCategories.find(cat => cat.id === defaultCategory.id);

    if (!category) {
      // Add the new category
      category = {
        id: defaultCategory.id,
        title: defaultCategory.title,
        items: []
      };
      updatedPreferences.sidebarCategories.push(category);
    }

    // Add new items to the category
    defaultCategory.items.forEach(defaultItem => {
      if (!category.items.some(item => item.id === defaultItem.id)) {
        // Add the new item with isVisible set to true by default
        // This ensures new features are shown to users by default
        category.items.push({
          ...defaultItem,
          isVisible: true
        });
      }
    });
  });

  // Merge quick links items
  defaultMenuStructure.quickLinksItems.forEach(defaultItem => {
    if (!updatedPreferences.quickLinksItems.some(item => item.id === defaultItem.id)) {
      // Add the new item with isVisible set to true by default
      // This ensures new features are shown to users by default
      updatedPreferences.quickLinksItems.push({
        ...defaultItem,
        isVisible: true
      });
    }
  });

  return updatedPreferences;
};
