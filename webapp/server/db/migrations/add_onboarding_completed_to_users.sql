-- Migration: Add onboarding_completed column to users table
-- This migration adds a boolean column to track whether a user has completed the onboarding process

SET search_path TO site;

-- Check if the column already exists
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1
        FROM information_schema.columns
        WHERE table_schema = 'site'
        AND table_name = 'users'
        AND column_name = 'onboarding_completed'
    ) THEN
        -- Add the onboarding_completed column with a default value of false
        ALTER TABLE users
        ADD COLUMN onboarding_completed BOOLEAN NOT NULL DEFAULT FALSE;

        -- Log the migration step
        PERFORM log_migration_step('Added onboarding_completed column to users table');
    ELSE
        -- Log that the column already exists
        RAISE NOTICE 'Column onboarding_completed already exists in users table, skipping';
    END IF;
END $$;