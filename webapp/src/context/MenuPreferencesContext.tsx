import React, { createContext, useState, useContext, useEffect, ReactNode } from 'react';
import axios from 'axios';
import { AuthContext } from './AuthContext';
import { FarmContext } from './FarmContext';
import { API_URL } from '../config';
import { 
  getUserMenuPreferences,
  getMockMenuPreferences
} from '../services/menuPreferencesService';
import { MenuItem, MenuCategory } from '../utils/menuUtils';
import {
  UserRole,
  PermissionType,
  getMockRolePermissions,
  getUserRoleForFarm,
  getFarmRolePermissions
} from '../services/rolePermissionService';
import { getIntegrations } from '../services/integrationService';

// Define interface for integration object
interface Integration {
  name: string;
  enabled: boolean;
  [key: string]: any; // Allow for additional properties
}

interface MenuPreferencesContextType {
  headerItems: MenuItem[];
  sidebarCategories: MenuCategory[];
  quickLinksItems: MenuItem[];
  loading: boolean;
  error: string | null;
  userRole: UserRole | null;
  enabledFeatures: {[key: string]: boolean};
  refreshPreferences: () => Promise<void>;
  reorderSidebarCategories: (categories: MenuCategory[]) => void;
  isFeatureEnabled: (featureId: string) => boolean;
}

const defaultMenuPreferencesContext: MenuPreferencesContextType = {
  headerItems: [],
  sidebarCategories: [],
  quickLinksItems: [],
  loading: true,
  error: null,
  userRole: null,
  enabledFeatures: {},
  refreshPreferences: async () => {},
  reorderSidebarCategories: () => {},
  isFeatureEnabled: () => true
};

export const MenuPreferencesContext = createContext<MenuPreferencesContextType>(defaultMenuPreferencesContext);

interface MenuPreferencesProviderProps {
  children: ReactNode;
}

export const MenuPreferencesProvider: React.FC<MenuPreferencesProviderProps> = ({ children }) => {
  const { user } = useContext(AuthContext);
  const { currentFarm } = useContext(FarmContext);
  const [headerItems, setHeaderItems] = useState<MenuItem[]>([]);
  const [sidebarCategories, setSidebarCategories] = useState<MenuCategory[]>([]);
  const [quickLinksItems, setQuickLinksItems] = useState<MenuItem[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [userRole, setUserRole] = useState<UserRole | null>(null);
  // const [userType, setUserType] = useState<string>('farmer'); // Unused state
  const [isBusinessOwner, setIsBusinessOwner] = useState<boolean>(false);
  const [isFarmOwner, setIsFarmOwner] = useState<boolean>(true);
  const [enabledFeatures, setEnabledFeatures] = useState<{[key: string]: boolean}>({});

  const fetchMenuPreferences = async () => {
    if (!user) return;

    setLoading(true);
    setError(null);

    try {
      // Fetch user details
      try {
        const userResponse = await axios.get(`${API_URL}/users/${user.id}/details`, {
          headers: {
            Authorization: `Bearer ${localStorage.getItem('token')}`
          }
        });

        // Set user details with default values if not provided
        const details = userResponse.data.user || {};
        const userType = details.user_type || 'farmer';
        const isBusinessOwner = details.is_business_owner || false;
        const isFarmOwner = userType === 'farmer' || false;

        // setUserType(userType); // Commented out as userType state is unused
        setIsBusinessOwner(isBusinessOwner);
        setIsFarmOwner(isFarmOwner);
      } catch (err) {
        console.error('Error fetching user details:', err);
        // Use default values if the API call fails
        // setUserType('farmer'); // Commented out as userType state is unused
        setIsBusinessOwner(false);
        setIsFarmOwner(true);
      }

      // Fetch installed integrations to check for plugins
      let installedIntegrations: Integration[] = [];
      // Only fetch integrations if we're not on the login page (user is authenticated and has a token)
      if (localStorage.getItem('token')) {
        try {
          installedIntegrations = await getIntegrations() as Integration[];
        } catch (err) {
          console.error('Error fetching installed integrations:', err);
          // Continue with empty integrations list if the API call fails
        }
      }

      // Check if market price tracker and field health analytics plugins are installed
      const isMarketPriceTrackerInstalled = installedIntegrations.some(
        integration => integration.name === 'Market Price Tracker' && integration.enabled
      );
      const isFieldHealthAnalyticsInstalled = installedIntegrations.some(
        integration => integration.name === 'Field Health Analytics' && integration.enabled
      );

      // Fetch the user's preferences from the backend
      let preferences;
      try {
        preferences = await getUserMenuPreferences(user.id);
      } catch (err) {
        console.error('Error fetching user menu preferences:', err);
        // Fallback to mock preferences if the API call fails
        try {
          preferences = await getMockMenuPreferences(user.id);
        } catch (mockErr) {
          console.error('Error fetching mock menu preferences:', mockErr);
          // If even the mock preferences fail, use an empty structure
          preferences = {
            userId: user.id,
            headerItems: [],
            sidebarCategories: [],
            quickLinksItems: []
          };
        }
      }

      // Determine the user's role
      let role: UserRole | null = null;

      // If user is a global admin, they see everything regardless of plugin installation status or permissions
      if (user.is_global_admin) {
        // For global admins, we want to respect the default header menu simplification
        // but ensure required items are visible
        const filteredHeaderItems = preferences.headerItems.map(item => {
          // Do not show admin-header as we already have the admin dropdown
          if (item.id === 'admin-header') {
            return {
              ...item,
              isVisible: false
            };
          }

          // Keep required items visible
          if (item.isRequired) {
            return {
              ...item,
              isVisible: true
            };
          }

          // Keep current visibility for other items
          return item;
        });

        // Global admins see all sidebar categories and items
        const filteredSidebarCategories = preferences.sidebarCategories.map(category => {
          const filteredItems = category.items.map(item => ({
            ...item,
            isVisible: true
          }));

          return {
            ...category,
            items: filteredItems,
            isVisible: true
          };
        });

        // Global admins see all quick links items
        const filteredQuickLinksItems = preferences.quickLinksItems.map(item => ({
          ...item,
          isVisible: true
        }));

        setHeaderItems(filteredHeaderItems);
        setSidebarCategories(filteredSidebarCategories);
        setQuickLinksItems(filteredQuickLinksItems);
        setUserRole(null); // No specific role for global admin
        setLoading(false);
        return;
      }

          // Fetch the user's role for the current farm
      if (currentFarm) {
        try {
          const userRole = await getUserRoleForFarm(user.id, currentFarm.id);
          if (userRole) {
            role = userRole;
          } else {
            // Fallback to default role if the API call fails
            role = 'farm_employee';
          }
        } catch (err) {
          console.error('Error fetching user role for farm:', err);
          // Fallback to default role if the API call fails
          role = 'farm_employee';
        }
      } else {
        // Fallback to default role if no current farm
        role = 'farm_employee';
      }

      setUserRole(role);

      // Get permissions for this role
      let rolePermissions;

      if (currentFarm) {
        try {
          // Try to get the actual permissions from the server
          rolePermissions = await getFarmRolePermissions(currentFarm.id, role);

          // If the API returns an empty array, fall back to mock permissions
          if (!rolePermissions || rolePermissions.length === 0) {
            console.log('No role permissions returned from API, using mock permissions');
            rolePermissions = getMockRolePermissions(role);
          }
        } catch (err) {
          console.error('Error fetching role permissions:', err);
          // Fallback to mock permissions if the API call fails
          rolePermissions = getMockRolePermissions(role);
        }
      } else {
        // Use mock permissions if no current farm
        rolePermissions = getMockRolePermissions(role);
      }

      // Create a map of feature to permissions for easier lookup
      const permissionsMap = rolePermissions.reduce((map, permission) => {
        map[permission.feature] = {
          view: permission.can_view,
          create: permission.can_create,
          edit: permission.can_edit,
          delete: permission.can_delete
        };
        return map;
      }, {} as { [key: string]: { [key in PermissionType]: boolean } });

      // Filter header items based only on explicit visibility settings and subscription features
      // Users should see all links available to them by default, and only menu links hidden by the menu customization system should affect what's shown
      const filteredHeaderItems = preferences.headerItems.map(item => {
        // Hide market prices header if not installed
        if (item.id === 'market-prices-header' && !isMarketPriceTrackerInstalled) {
          return {
            ...item,
            isVisible: false
          };
        }

        // Check if this feature is enabled in the subscription plan
        const featureEnabled = isFeatureEnabled(item.id);

        // Only respect the item's existing visibility setting if it's false (explicitly hidden by user)
        // Otherwise, show the item if the feature is enabled in the subscription plan
        return {
          ...item,
          isVisible: item.isVisible !== false && featureEnabled
        };
      });

      // Filter sidebar categories and their items based only on explicit visibility settings and subscription features
      // Users should see all links available to them by default, and only menu links hidden by the menu customization system should affect what's shown
      const filteredSidebarCategories = preferences.sidebarCategories.map(category => {
        const filteredItems = category.items.map(item => {
          // Hide subscriptions for business accounts that are not also farm owners
          if (item.id === 'subscriptions' && isBusinessOwner && !isFarmOwner) {
            return {
              ...item,
              isVisible: false
            };
          }

          // Hide market price tracker if not installed
          if (item.id === 'market-prices' && !isMarketPriceTrackerInstalled) {
            return {
              ...item,
              isVisible: false
            };
          }

          // Hide field health analytics if not installed
          if (item.id === 'field-health' && !isFieldHealthAnalyticsInstalled) {
            return {
              ...item,
              isVisible: false
            };
          }

          // Check if this feature is enabled in the subscription plan
          const featureEnabled = isFeatureEnabled(item.id);

          // Only respect the item's existing visibility setting if it's false (explicitly hidden by user)
          // Otherwise, show the item if the feature is enabled in the subscription plan
          return {
            ...item,
            isVisible: item.isVisible !== false && featureEnabled
          };
        });

        // Only include categories that have at least one visible item
        return {
          ...category,
          items: filteredItems,
          // A category is visible if it has at least one visible item
          isVisible: filteredItems.some(item => item.isVisible)
        };
      }).filter(category => category.isVisible);

      // Filter quick links items based only on explicit visibility settings and subscription features
      // Users should see all links available to them by default, and only menu links hidden by the menu customization system should affect what's shown
      const filteredQuickLinksItems = preferences.quickLinksItems.map(item => {
        // Hide market price tracker if not installed
        if (item.id === 'market-prices-quick' && !isMarketPriceTrackerInstalled) {
          return {
            ...item,
            isVisible: false
          };
        }

        // Hide field health analytics if not installed
        if (item.id === 'field-health-quick' && !isFieldHealthAnalyticsInstalled) {
          return {
            ...item,
            isVisible: false
          };
        }

        // Check if this feature is enabled in the subscription plan
        const featureEnabled = isFeatureEnabled(item.id);

        // Only respect the item's existing visibility setting if it's false (explicitly hidden by user)
        // Otherwise, show the item if the feature is enabled in the subscription plan
        return {
          ...item,
          isVisible: item.isVisible !== false && featureEnabled
        };
      });

      setHeaderItems(filteredHeaderItems);
      setSidebarCategories(filteredSidebarCategories);
      setQuickLinksItems(filteredQuickLinksItems);
    } catch (err: any) {
      console.error('Error fetching menu preferences:', err);
      setError('Failed to load menu preferences.');
    } finally {
      setLoading(false);
    }
  };

  // Fetch menu preferences when the user or current farm changes
  // Fetch the current farm's subscription plan features
  const fetchSubscriptionFeatures = async () => {
    if (!currentFarm) {
      setEnabledFeatures({});
      return;
    }

    try {
      // Fetch the farm's subscription plan
      const response = await axios.get(`${API_URL}/farms/${currentFarm.id}`, {
        headers: {
          Authorization: `Bearer ${localStorage.getItem('token')}`
        }
      });

      const farm = response.data.farm;

      // If the farm has a subscription plan with features, use them
      if (farm.SubscriptionPlan && farm.SubscriptionPlan.features) {
        setEnabledFeatures(farm.SubscriptionPlan.features);
      } else {
        // Otherwise, enable all features by default
        setEnabledFeatures({});
      }
    } catch (err) {
      console.error('Error fetching subscription features:', err);
      // If there's an error, enable all features by default
      setEnabledFeatures({});
    }
  };

  // Check if a feature is enabled
  const isFeatureEnabled = (featureId: string) => {
    // Global admins have access to all features
    if (user?.is_global_admin) {
      return true;
    }

    // If there are no enabled features specified, all features are enabled
    if (Object.keys(enabledFeatures).length === 0) {
      return true;
    }

    // If the feature is not explicitly disabled, consider it enabled
    // This ensures all menu items show up by default
    return enabledFeatures[featureId] !== false;
  };

  useEffect(() => {
    fetchMenuPreferences();
    fetchSubscriptionFeatures();
  }, [user, currentFarm]);

  const refreshPreferences = async () => {
    await fetchMenuPreferences();
  };

  const reorderSidebarCategories = (categories: MenuCategory[]) => {
    setSidebarCategories(categories);
  };

  return (
    <MenuPreferencesContext.Provider
      value={{
        headerItems,
        sidebarCategories,
        quickLinksItems,
        loading,
        error,
        userRole,
        enabledFeatures,
        refreshPreferences,
        reorderSidebarCategories,
        isFeatureEnabled
      }}
    >
      {children}
    </MenuPreferencesContext.Provider>
  );
};
